import webview
from apis import API


def create_window():
    """创建窗口并正确绑定API"""
    url = 'http://localhost:5173'

    # 1. 先创建API实例（此时不需要window参数）


    # 2. 创建窗口并传递API实例
    window = webview.create_window(
        title='办公助手',
        url=url,
        width=800,
        height=600,
        resizable=True,

    )

    # 3. 将window对象传递给API（如果需要）
    window._js_api = API(window)

    return window


if __name__ == '__main__':
    # 创建窗口
    window = create_window()

    # 启动webview
    webview.start(debug=True)
