import webview
from apis import API


if __name__ == '__main__':
    """创建窗口并正确绑定API"""
    url = 'http://localhost:5173'

    # 创建API实例（先传递None，稍后设置window）
    api = API(None)

    # 创建窗口并传递API
    window = webview.create_window(
        title='办公助手',
        url=url,
        width=800,
        height=600,
        resizable=True,
        js_api=api
    )

    # 将window对象设置到API实例中
    api._window = window

    # 启动webview
    webview.start(debug=True)
