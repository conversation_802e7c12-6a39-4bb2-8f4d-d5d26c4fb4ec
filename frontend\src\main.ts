import {createApp} from 'vue'
import {createPinia} from 'pinia'
import router from '@/router'
import App from './App.vue'

// 样式导入
// 注意：使用 SASS 主题定制时，不要导入 element-plus/dist/index.css
// Element Plus 的样式会通过 vite.config.ts 中的配置自动处理
import 'animate.css'
import '@/style.css'

// Font Awesome 图标
import {library} from '@fortawesome/fontawesome-svg-core'
import {FontAwesomeIcon} from '@fortawesome/vue-fontawesome'

import {
    // 已有的图标
    faUser,
    faLock,
    faEye,
    faEyeSlash,
    faSignInAlt,
    faSpinner,
    faInfoCircle,
    faBriefcase,

    // 新增登录页面需要的图标

} from '@fortawesome/free-solid-svg-icons'
import {
    faWeixin,
    faQq,
    faMicrosoft
} from '@fortawesome/free-brands-svg-icons'
// 添加品牌图标需要单独引入


// 添加图标到库
library.add(
    faUser, faLock, faEye, faEyeSlash, faSignInAlt, faSpinner,
    faInfoCircle, faBriefcase, faWeixin,
    faQq,
    faMicrosoft
)

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.component('font-awesome-icon', FontAwesomeIcon)

app.mount('#app')
