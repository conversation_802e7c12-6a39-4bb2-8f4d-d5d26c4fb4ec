class API:
    def __init__(self, window):
        # 保留窗口引用（可能初始为None）
        # 使用私有属性避免被暴露到JS
        self._window = window

    def resize_window(self, width, height):
        """调整窗口大小"""
        if not self._window:
            return {"success": False, "message": "窗口对象未初始化"}

        try:
            self._window.resize(width, height)
            return {"success": True, "message": f"窗口大小已调整为 {width}x{height}"}
        except Exception as e:
            return {"success": False, "message": f"调整窗口大小失败: {str(e)}"}

    def say_hello(self, name):
        """测试方法 - 用于验证API绑定是否正常"""
        return f"Hello, {name}! API绑定成功！"

    def get_window_info(self):
        """获取当前窗口信息"""
        if not self._window:
            return {"success": False, "message": "窗口对象未初始化"}

        try:
            # 注意：pywebview 可能没有直接获取窗口大小的方法
            # 这里返回一些基本信息
            return {
                "success": True,
                "title": "办公助手",
                "resizable": True
            }
        except Exception as e:
            return {"success": False, "message": f"获取窗口信息失败: {str(e)}"}
