<template>
  <div class="home-container">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>Vue3 + Element Plus + PyWebView</span>
          <font-awesome-icon :icon="['fas', 'home']"/>
        </div>
      </template>

      <div class="demo-content">
        <!-- 计数器部分保持不变 -->
        <h3>计数器示例 (Pinia)</h3>
        <p>当前计数: {{ count }}</p>
        <p>双倍计数: {{ doubleCount }}</p>
        <p>是否为偶数: {{ isEven ? '是' : '否' }}</p>

        <div class="button-group">
          <el-button type="primary" @click="increment">
            <font-awesome-icon :icon="['fas', 'plus']"/>
            增加
          </el-button>
          <el-button type="danger" @click="decrement">
            <font-awesome-icon :icon="['fas', 'minus']"/>
            减少
          </el-button>
          <el-button @click="reset">
            <font-awesome-icon :icon="['fas', 'refresh']"/>
            重置
          </el-button>
        </div>

        <!-- PyWebView API 测试区域 -->
        <div class="api-test-section">
          <h3>PyWebView API 测试</h3>

          <div class="mt-4">
            <el-input
                v-model="apiInput"
                placeholder="输入测试消息"
                style="width: 200px; margin-right: 10px;"
            />
            <el-button type="primary" @click="callPythonAPI">
              调用Python方法
            </el-button>
            <p v-if="apiResponse">Python响应: {{ apiResponse }}</p>
          </div>

          <div class="mt-4">
            <el-button type="success" @click="showFingerprint">
              获取浏览器指纹
            </el-button>
            <p v-if="fingerprint">指纹ID: {{ fingerprint }}</p>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {storeToRefs} from 'pinia'
import {useCounterStore} from '@/store/counter'
import {getBrowserFingerprint} from '@/utils/fingerprint'
import {ElMessage} from 'element-plus'
import { testApiConnection, isPyWebViewEnvironment } from '@/utils/pywebview'

// 计数器逻辑（保持不变）
const counterStore = useCounterStore()
const {count, doubleCount, isEven} = storeToRefs(counterStore)
const {increment, decrement, reset, setCount} = counterStore

// PyWebView 相关状态
const apiInput = ref('')
const apiResponse = ref('')
const fingerprint = ref('')

// 检查是否在 pywebview 环境中
const isPyWebView = ref(isPyWebViewEnvironment())

// 调用 Python 方法
const callPythonAPI = async () => {
  try {
    const response = await testApiConnection(apiInput.value || 'Home页面')
    apiResponse.value = response
    ElMessage.success('API调用成功')
  } catch (error) {
    ElMessage.error('API调用失败: ' + error)
    console.error('API Error:', error)
  }
}

// 获取浏览器指纹
const showFingerprint = async () => {
  try {
    fingerprint.value = await getBrowserFingerprint()
    ElMessage.success('获取指纹成功')
  } catch (error) {
    ElMessage.error('获取指纹失败: ' + error.message)
  }
}

</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.home-container {
  padding: $spacing-lg;
  display: flex;
  justify-content: center;
}

.demo-card {
  width: 100%;
  max-width: 800px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.2rem;
}

.demo-content {
  padding: $spacing-md;

  h3 {
    margin-bottom: $spacing-md;
    color: var(--el-color-primary);
  }
}

.button-group {
  margin: $spacing-md 0;

  .el-button {
    margin: 0 $spacing-xs;
  }
}

.api-test-section {
  margin-top: $spacing-lg;
  padding-top: $spacing-lg;
  border-top: 1px dashed var(--el-border-color);

  h3 {
    color: var(--el-color-success);
  }
}

.mt-4 {
  margin-top: $spacing-md;
}

p {
  margin: $spacing-xs 0;
  word-break: break-all;
}
</style>