<template>
  <div class="login-container">
    <div class="login-background">
      <div class="background-gradient"></div>
      <div class="floating-element"></div>
      <div class="floating-element"></div>
    </div>

    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <font-awesome-icon :icon="['fas', 'briefcase']" class="logo-icon"/>
          <span>OFFICE</span>
        </div>
        <h1 class="title">办公助手</h1>
        <p class="subtitle">高效 · 安全 · 智能</p>
      </div>

      <div class="login-form">
        <div class="input-group">
          <font-awesome-icon :icon="['fas', 'user']" class="input-icon"/>
          <input
              v-model="username"
              type="text"
              placeholder="用户名"
              class="login-input"
          />
        </div>

        <div class="input-group">
          <font-awesome-icon :icon="['fas', 'lock']" class="input-icon"/>
          <input
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="密码"
              class="login-input"
              @keyup.enter="login"
          />
          <div class="password-toggle" @click="togglePassword">
            <font-awesome-icon :icon="['fas', showPassword ? 'eye-slash' : 'eye']"/>
          </div>
        </div>

        <div class="remember-forgot">
          <label class="remember-me">
            <input type="checkbox" v-model="rememberMe" class="styled-checkbox"/>
            <span>记住我</span>
          </label>
          <a href="#" class="forgot-password">忘记密码?</a>
        </div>

        <button
            class="login-button"
            :class="{'loading': isLoading}"
            @click="login"
        >
          <font-awesome-icon :icon="['fas', 'sign-in-alt']" v-if="!isLoading"/>
          {{ isLoading ? '登录中...' : '登录' }}
        </button>

        <div class="social-divider">
          <span>或使用其他方式登录</span>
        </div>

        <div class="social-login">
          <button class="social-button wechat">
            <font-awesome-icon :icon="['fab', 'weixin']"/>
          </button>
          <button class="social-button qq">
            <font-awesome-icon :icon="['fab', 'qq']"/>
          </button>
          <button class="social-button microsoft">
            <font-awesome-icon :icon="['fab', 'microsoft']"/>
          </button>
        </div>
      </div>

      <div class="login-footer">
        <p>© 2023 办公助手系统 | <a href="#">隐私政策</a> | <a href="#">使用条款</a></p>
      </div>
    </div>

    <div class="login-notice" v-if="showNotice">
      <font-awesome-icon :icon="['fas', 'info-circle']"/>
      <span>{{ noticeMessage }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {useRouter} from 'vue-router'
import { applyWindowSizePreset } from '@/utils/pywebview'

const router = useRouter()

const username = ref('')
const password = ref('')
const rememberMe = ref(true)
const showPassword = ref(false)
const isLoading = ref(false)
const showNotice = ref(false)
const noticeMessage = ref('')

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const login = async () => {
  if (!username.value || !password.value) {
    showNotice.value = true
    noticeMessage.value = '请输入用户名和密码'
    setTimeout(() => showNotice.value = false, 3000)
    return
  }

  isLoading.value = true

  // 模拟登录请求
  setTimeout(async () => {
    isLoading.value = false
    showNotice.value = true
    noticeMessage.value = '登录成功，正在跳转...'

    // 调用 pywebview API 调整窗口大小
    try {
      // 登录后调整窗口大小为主界面尺寸
      const result = await applyWindowSizePreset('MAIN')
      if (result.success) {
        console.log('窗口大小调整成功:', result.message)
      } else {
        console.log('窗口大小调整失败:', result.message)
      }
    } catch (error) {
      console.error('调整窗口大小失败:', error)
    }

    // 跳转到首页
    setTimeout(() => {
      router.push('/home')
    }, 1000)
  }, 1500)
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  min-width: 100vw;
  position: relative;
  overflow: hidden;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #6a5de0 0%, #2575fc 100%);
  padding: 0;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  .background-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(106, 93, 224, 0.1) 0%, rgba(37, 117, 252, 0.1) 100%);
    opacity: 0.8;
  }

  .floating-element {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    filter: blur(20px);

    &:first-child {
      width: 200px;
      height: 200px;
      top: -50px;
      right: -50px;
      background: linear-gradient(135deg, rgba(106, 93, 224, 0.1) 0%, rgba(72, 126, 223, 0.1) 100%);
    }

    &:last-child {
      width: 150px;
      height: 150px;
      bottom: -30px;
      left: -30px;
      background: linear-gradient(135deg, rgba(46, 192, 247, 0.1) 0%, rgba(72, 126, 223, 0.1) 100%);
    }
  }
}

.login-card {
  width: 400px;
  height: 600px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 4px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  z-index: 2;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.login-header {
  text-align: center;
  margin-bottom: 20px;

  .logo {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #6a5de0 0%, #487edf 100%);
    border-radius: 12px;
    padding: 8px 12px;
    margin-bottom: 10px;

    .logo-icon {
      font-size: 20px;
      color: white;
      margin-right: 8px;
    }

    span {
      color: white;
      font-weight: 600;
      font-size: 16px;
      letter-spacing: 1px;
    }
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 5px;
    letter-spacing: 0.5px;
  }

  .subtitle {
    color: #6a5de0;
    font-size: 12px;
    letter-spacing: 1px;
    font-weight: 500;
    margin: 0;
  }
}

.login-form {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .input-group {
    position: relative;
    margin-bottom: 15px;

    .input-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #6a5de0;
      font-size: 14px;
    }

    .password-toggle {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #a9b1cc;
      cursor: pointer;
      font-size: 14px;
      transition: color 0.3s;

      &:hover {
        color: #6a5de0;
      }
    }
  }

  .login-input {
    width: 100%;
    padding: 12px 12px 12px 36px;
    border: 1px solid #e2e6f1;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s;
    background: #f8fafd;
    color: #444;
    height: 42px;

    &:focus {
      outline: none;
      border-color: #6a5de0;
      box-shadow: 0 0 0 2px rgba(106, 93, 224, 0.2);
      background: #fff;
    }

    &::placeholder {
      color: #a9b1cc;
      font-size: 13px;
    }
  }

  .remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 13px;

    .remember-me {
      display: flex;
      align-items: center;
      color: #666;
      cursor: pointer;

      .styled-checkbox {
        position: relative;
        margin-right: 6px;
        width: 16px;
        height: 16px;
        border: 1px solid #d1d6e6;
        border-radius: 4px;
        appearance: none;
        cursor: pointer;

        &:checked {
          background: #6a5de0;
          border-color: #6a5de0;

          &::after {
            content: '';
            position: absolute;
            left: 4px;
            top: 1px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
          }
        }
      }
    }

    .forgot-password {
      color: #6a5de0;
      text-decoration: none;
      transition: color 0.3s;
      font-weight: 500;

      &:hover {
        color: #4c3fd7;
        text-decoration: underline;
      }
    }
  }

  .login-button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #6a5de0 0%, #487edf 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 10px rgba(106, 93, 224, 0.3);
    height: 42px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(106, 93, 224, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    &.loading {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }

  .social-divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
    color: #bdc3c7;
    font-size: 12px;

    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background: #e0e0e0;
    }

    span {
      padding: 0 10px;
    }
  }

  .social-login {
    display: flex;
    justify-content: center;
    gap: 15px;

    .social-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      background: #f8fafd;
      color: #888;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
      }

      &.wechat:hover {
        background: #09bb07;
        color: white;
      }

      &.microsoft:hover {
        background: #737373;
        color: white;
      }

      &.qq:hover {
        background: #12b7f5;
        color: white;
      }
    }
  }
}

.login-footer {
  margin-top: 10px;
  text-align: center;
  color: #7f8c8d;
  font-size: 11px;

  a {
    color: #6a5de0;
    text-decoration: none;
    margin: 0 3px;

    &:hover {
      text-decoration: underline;
    }
  }
}

.login-notice {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(106, 93, 224, 0.9);
  color: white;
  padding: 10px 18px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  z-index: 100;
  box-shadow: 0 4px 12px rgba(106, 93, 224, 0.25);
}
</style>