import {createRouter, createWebHistory} from 'vue-router'
import type {RouteRecordRaw} from 'vue-router'

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        redirect: '/login',
        meta: {
            title: '/'
        }
    },
    {
        path: '/home',
        name: 'home',
        component: () => import('@/views/home.vue'),
        meta: {
            width: 1200,
            height: 900,
        }
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/Login.vue')
    },
    {
        path: '/about',
        name: 'About',
        component: () => import('@/views/About.vue'),
        meta: {
            title: '关于'
        }
    },
    {
        path: '/icon-test',
        name: 'IconTest',
        component: () => import('@/views/IconTest.vue'),
        meta: {
            title: '图标测试'
        }
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
    // 1. 设置页面标题
    if (to.meta?.title) {
        document.title = `${to.meta.title} - 办公助手`
    }

    // 2. 安全检查WebView环境
    const hasWebViewAPI = !!(window.pywebview?.api?.resize_window)

    // 3. 调整窗口大小（仅在WebView环境下）
    if (hasWebViewAPI && to.meta?.width && to.meta?.height) {
        try {
            window.pywebview.api.resize_window(to.meta.width, to.meta.height)
        } catch (error) {
            console.error('调整窗口大小失败:', error)
        }
    }

    next()
})

export default router
