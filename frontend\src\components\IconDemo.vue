<template>
  <el-card class="icon-demo">
    <template #header>
      <div class="card-header">
        <span>Font Awesome 图标示例</span>
        <font-awesome-icon :icon="['fas', 'cog']" />
      </div>
    </template>
    
    <div class="icon-grid">
      <div class="icon-item" v-for="icon in icons" :key="icon.name">
        <font-awesome-icon :icon="['fas', icon.name]" class="icon" />
        <span class="icon-name">{{ icon.label }}</span>
      </div>
    </div>
    
    <el-divider />
    
    <div class="usage-example">
      <h4>使用示例：</h4>
      <pre class="code-example">
&lt;font-awesome-icon :icon="['fas', 'user']" /&gt;
      </pre>
    </div>
  </el-card>
</template>

<script setup lang="ts">
const icons = [
  { name: 'user', label: '用户' },
  { name: 'lock', label: '锁定' },
  { name: 'home', label: '首页' },
  { name: 'cog', label: '设置' },
  { name: 'plus', label: '添加' },
  { name: 'minus', label: '减少' },
  { name: 'refresh', label: '刷新' },
  { name: 'search', label: '搜索' },
  { name: 'edit', label: '编辑' },
  { name: 'trash', label: '删除' },
  { name: 'save', label: '保存' },
  { name: 'check', label: '确认' },
  { name: 'times', label: '关闭' },
  { name: 'eye', label: '查看' },
  { name: 'eye-slash', label: '隐藏' },
  { name: 'download', label: '下载' },
  { name: 'upload', label: '上传' },
  { name: 'arrow-left', label: '左箭头' },
  { name: 'arrow-right', label: '右箭头' }
]
</script>

<style scoped lang="scss">
@import "@/styles/variables.scss";

.icon-demo {
  margin: $spacing-md 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-md;
  border: 1px solid $border-light;
  border-radius: $border-radius-base;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: $primary-color;
    box-shadow: $box-shadow-light;
  }
}

.icon {
  font-size: 24px;
  color: $primary-color;
  margin-bottom: $spacing-sm;
}

.icon-name {
  font-size: 12px;
  color: $text-secondary;
  text-align: center;
}

.usage-example {
  h4 {
    margin-bottom: $spacing-sm;
    color: $text-primary;
  }

  .code-example {
    background-color: #f5f5f5;
    border: 1px solid $border-light;
    border-radius: $border-radius-base;
    padding: $spacing-md;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #333;
    margin: 0;
  }
}
</style>
